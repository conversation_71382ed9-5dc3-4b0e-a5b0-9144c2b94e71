from typing import Dict, Any
from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from langchain_openai import Chat<PERSON>penAI
from langchain.prompts.chat import ChatPromptTemplate, HumanMessagePromptTemplate
from langchain.output_parsers import PydanticOutputParser
import json

from models.ppt_models import (
    GENERAL_PROMPT_TEMPLATE,
    TATTOO_PROMPT_TEMPLATE,
    BARBER_PROMPT_TEMPLATE,
)
from models.user_model import FirebaseData


class AiDataTransformer:
    def __init__(self, gemini_api_key: str, openai_api_key: str):
        self.gemini_api_key = gemini_api_key
        self.openai_api_key = openai_api_key

    def create_transformer(self, model: str = "gemini") -> Any:
        """
        Create a transformer using either Gemini or OpenAI model.

        Args:
            model: Which model to use ("gemini" or "openai")

        Returns:
            The configured model
        """
        # Setup the parser with our Pydantic model
        parser = PydanticOutputParser(pydantic_object=FirebaseData)

        # Create the prompt template
        ChatPromptTemplate(
            messages=[HumanMessagePromptTemplate.from_template(BARBER_PROMPT_TEMPLATE)],
            partial_variables={"format_instructions": parser.get_format_instructions()},
        )

        # Return the appropriate model
        return (
            ChatGoogleGenerativeAI(
                model="gemini-2.5-pro",
                # model="gemini-2.5-flash",
                google_api_key=self.gemini_api_key,
                temperature=0.2,
            )
            if model == "gemini"
            else ChatOpenAI(
                model="gpt-4o-mini", api_key=self.openai_api_key, temperature=0.1
            )
        )

    def transform(
        self,
        input_json: Dict[str, Any],
        model: str = "gemini",
        additional_info: str = "",
    ) -> Dict[str, Any]:
        """
        Transform JSON data using AI to optimize it for link-in-bio pages.

        Args:
            input_json: The JSON data to transform
            model: AI model to use ("gemini" or "openai")
            additional_info: Additional context information for processing
        """
        try:
            # Create parser and prompt
            parser = PydanticOutputParser(pydantic_object=FirebaseData)
            prompt = ChatPromptTemplate(
                messages=[
                    HumanMessagePromptTemplate.from_template(BARBER_PROMPT_TEMPLATE)
                ],
                input_variables=["input_json", "additional_info"],
                partial_variables={
                    "format_instructions": parser.get_format_instructions()
                },
            )

            # Create AI model
            llm = self.create_transformer(model)

            # Prepare additional info text
            info_text = (
                additional_info
                if additional_info
                else "Nenhuma informação adicional fornecida."
            )

            # Format prompt with input JSON and additional info
            _input = prompt.format_prompt(
                input_json=json.dumps(input_json, ensure_ascii=False),
                additional_info=info_text,
            )

            # Get AI response
            output = llm.invoke(_input.to_messages())

            # Parse the response
            transformed_data = parser.parse(output.content)
            final_data = transformed_data.model_dump()

            # Ensure all required sections exist in each user profile
            if "users" in final_data:
                for username, profile in final_data["users"].items():
                    # Ensure empty lists/objects for missing sections
                    if "socialMedias" not in profile:
                        profile["socialMedias"] = []

                    if "links" not in profile:
                        profile["links"] = []

                    if "gallery" not in profile:
                        profile["gallery"] = {
                            "enabled": False,
                            "title": "",
                            "description": "",
                            "images": [],
                        }

                    if "genericSection" not in profile:
                        profile["genericSection"] = {
                            "enabled": False,
                            "items": [],
                            "description": "",
                            "title": "",
                        }

                    if "servicesSection" not in profile:
                        profile["servicesSection"] = {
                            "enabled": False,
                            "items": [],
                            "description": "",
                            "title": "",
                        }

                    if "featuresSection" not in profile:
                        profile["featuresSection"] = {
                            "enabled": False,
                            "title": "",
                            "description": "",
                            "items": [],
                        }

                    if "video" not in profile:
                        profile["video"] = {
                            "description": "",
                            "showVideo": False,
                            "title": "",
                            "youtubeId": "",
                            "youtubeUrl": "",
                        }

                    if "reviews" not in profile:
                        profile["reviews"] = {
                            "description": "",
                            "reviews": [],
                            "showReviews": False,
                            "title": "Avaliações",
                        }

                    if "settings" not in profile:
                        profile["settings"] = {
                            "colors": {
                                "background": "#ffffff",
                                "linkText": "#fff",
                                "primary": "#156882",
                                "secondary": "#646464",
                                "socialIconBackground": "#dddddd",
                            },
                            "favicon": "",
                            "ogImage": "",
                            "pageDescription": "",
                            "pageKeywords": "",
                        }

                    if "user" not in profile:
                        profile["user"] = {
                            "bio": "",
                            "name": username,
                            "avatar": "",
                            "brandLogo": "",
                            "heroImage": "",
                            "username": f"{username}",
                        }

                    # Ensure heroImage defaults to avatar if empty
                    if "user" in profile:
                        if (
                            "heroImage" not in profile["user"]
                            or not profile["user"]["heroImage"]
                        ):
                            profile["user"]["heroImage"] = profile["user"].get(
                                "avatar", ""
                            )

                    # Ensure brandLogo defaults to avatar if empty
                    if "user" in profile:
                        if (
                            "brandLogo" not in profile["user"]
                            or not profile["user"]["brandLogo"]
                        ):
                            profile["user"]["brandLogo"] = profile["user"].get(
                                "avatar", ""
                            )

                    # Ensure ogImage defaults to favicon if empty
                    if "settings" in profile:
                        if (
                            "ogImage" not in profile["settings"]
                            or not profile["settings"]["ogImage"]
                        ):
                            profile["settings"]["ogImage"] = profile["settings"].get(
                                "favicon", ""
                            )

                    # Ensure team has description field
                    if "team" in profile and profile["team"].get("enabled", False):
                        if "description" not in profile["team"]:
                            profile["team"]["description"] = ""

            return final_data
        except Exception as e:
            print(f"Error parsing AI response: {e}")
            return input_json
