from typing import Dict, Any, Optional
import logging
import os
import json
import asyncio
from linktree_scraper import LinktreeScraper
from api.core.metrics import app_metrics
from api.core.config import settings
from services.utils.normalize_username import normalize_username
from core.constants import FIREBASE_URL
from data_handler import process_and_upload_json
from services.upload_avatar import upload_profile_image

logger = logging.getLogger(__name__)


class ScraperService:
    """Serviço responsável pelo scraping de dados do Linktree"""

    def __init__(self):
        self.scraper = LinktreeScraper()

    async def scrape_user_by_username(
        self, username: str, url: str, info: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Realiza o scraping de um usuário do Linktree

        Args:
            username: Nome de usuário do Linktree (já processado e validado)
            url: URL completa do Linktree (já processada e validada)
            info: Informações adicionais para contexto (opcional)

        Returns:
            Dict contendo os dados do usuário ou informações de erro
        """
        try:
            logger.info(f"Starting scrape for user: {username}")
            if info:
                logger.info(
                    f"Additional info provided: {info[:100]}..."
                )  # Log first 100 chars

            logger.info(f"Using URL: {url}")
            logger.info(f"Using username: {username}")

            # Realizar o scraping (versão simplificada sem salvar arquivos)
            user_info = await self._get_user_info_without_files(
                url=url, username=username
            )

            if not user_info:
                return {
                    "success": False,
                    "message": "Usuário não encontrado ou dados não disponíveis",
                    "data": None,
                }

            # Incrementar contador de scrapes
            app_metrics.increment_scrapes()

            # Converter para dict
            user_data = {
                "username": user_info.username,
                "url": user_info.url,
                "avatar_image": user_info.avartar_image,
                "id": user_info.id,
                "tier": user_info.tier,
                "isActive": user_info.isActive,
                "description": user_info.description,
                "createdAt": user_info.createdAt,
                "updatedAt": user_info.updatedAt,
                "number_of_links": len(user_info.links),
                "links": [
                    {"url": link.url, "button_text": link.button_text}
                    for link in user_info.links
                ],
            }

            logger.info(f"Successfully scraped user: {username}")

            return {
                "success": True,
                "message": "Scraping realizado com sucesso",
                "data": user_data,
            }

        except Exception as e:
            error_message = str(e)
            logger.error(f"Error scraping user {username}: {error_message}")

            # Verificar se é um erro 404 (perfil não encontrado)
            if "404" in error_message or "Not Found" in error_message:
                return {
                    "success": False,
                    "message": f"Perfil '{username}' não encontrado no Linktree. Verifique se o nome de usuário está correto e se o perfil existe.",
                    "data": None,
                }

            # Outros erros de rede
            elif "ClientResponseError" in error_message:
                return {
                    "success": False,
                    "message": f"Erro de conexão ao acessar o perfil '{username}'. O perfil pode estar indisponível ou não existir.",
                    "data": None,
                }

            # Outros erros gerais
            return {
                "success": False,
                "message": f"Erro durante o scraping: {error_message}",
                "data": None,
            }

    async def _get_user_info_without_files(
        self, url: Optional[str] = None, username: Optional[str] = None
    ):
        """
        Versão simplificada do getLinktreeUserInfo que não salva arquivos
        """
        if url is None and username is None:
            raise ValueError("Please pass linktree username or url.")

        # Obter dados JSON do usuário (sempre passar URL construída)
        final_url = url if url else f"https://linktr.ee/{username}"
        JSON_INFO = await self.scraper.getUserInfoJSON(url=final_url, username=username)
        account = JSON_INFO["account"]
        username = account["username"]
        avatar_image = account["profilePictureUrl"]
        url = f"https://linktr.ee/{username}" if url is None else url
        id = account["id"]
        tier = account.get("tier")
        isActive = account.get("isActive", False)
        createdAt = account.get("createdAt", 0)
        updatedAt = account.get("updatedAt", 0)
        description = account.get("description")

        # Obter links do usuário
        links = await self.scraper.getUserLinks(data=JSON_INFO)

        # Importar LinktreeUser para criar o objeto
        from linktree_scraper import LinktreeUser

        user_info = LinktreeUser(
            username=username,
            url=url,
            avartar_image=avatar_image,
            id=id,
            tier=tier,
            isActive=isActive,
            createdAt=createdAt,
            updatedAt=updatedAt,
            description=description,
            links=links,
        )

        # NÃO chamar generate_unified_json para evitar salvar arquivos
        return user_info

    async def process_user_complete(
        self, username: str, url: str, info: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Realiza o processo completo de scraping e upload para Firebase

        Este método executa todo o processo equivalente ao comando:
        python linktree.py USERNAME

        Ou seja, realiza:
        1. Extração dos dados do usuário
        2. Upload da imagem de perfil
        3. Geração do JSON unificado
        4. Processamento com IA
        5. Upload para Firebase

        Args:
            username: Nome de usuário do Linktree (já processado e validado)
            url: URL completa do Linktree (já processada e validada)
            info: Informações adicionais para contexto (opcional)

        Returns:
            Dict contendo o resultado do processo completo
        """
        try:
            logger.info(f"Starting complete process for user: {username}")
            if info:
                logger.info(
                    f"Additional info provided: {info[:100]}..."
                )  # Log first 100 chars

            # Criar diretório de output se não existir
            output_dir = os.path.join(os.getcwd(), "output")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 1. Obter informações do usuário
            logger.info(f"Fetching data for {username}...")
            user_info = await self.scraper.getLinktreeUserInfo(
                username=username, url=url
            )

            if not user_info:
                return {
                    "success": False,
                    "message": "Usuário não encontrado ou dados não disponíveis",
                    "data": None,
                }

            # Criar diretório específico do usuário
            user_dir = os.path.join(output_dir, user_info.username)
            if not os.path.exists(user_dir):
                os.makedirs(user_dir)

            logger.info(f"Found user information for: {user_info.username}")

            # 2. Upload da imagem de perfil para ImgBB se existir
            await upload_profile_image(user_info)

            # 3. Gerar JSON unificado
            logger.info("Generating unified JSON...")
            await self.scraper.generate_unified_json(user_info, user_info.username)

            # 4. Ler o JSON unificado gerado
            normalized_username = normalize_username(user_info.username)
            normalized_user_dir = os.path.join(output_dir, normalized_username)
            unified_json_path = os.path.join(
                normalized_user_dir, f"{normalized_username}.json"
            )

            # Aguardar criação do arquivo
            max_retries = 5
            for i in range(max_retries):
                if os.path.exists(unified_json_path):
                    break
                logger.info(
                    f"Waiting for JSON to be created (attempt {i+1}/{max_retries})..."
                )
                await asyncio.sleep(1)

            if not os.path.exists(unified_json_path):
                raise FileNotFoundError(
                    f"JSON file was not created in {unified_json_path}"
                )

            with open(unified_json_path, "r", encoding="utf-8") as f:
                unified_data = json.load(f)

            # 5. Processamento com IA e upload para Firebase
            logger.info("Optimizing data with AI for link page format...")

            # Criar estrutura esperada para Firebase
            firebase_input = {
                user_info.username: unified_data,
            }

            # Processar e enviar para Firebase
            await process_and_upload_json(
                firebase_input, use_ai=True, additional_info=info or ""
            )

            # Incrementar contador de scrapes
            app_metrics.increment_scrapes()

            # URL final no Firebase
            firebase_url = (
                f"{FIREBASE_URL}/users/{normalize_username(user_info.username)}.json"
            )

            # Converter para dict de resposta
            user_data = {
                "username": user_info.username,
                "url": user_info.url,
                "avatar_image": user_info.avartar_image,
                "id": user_info.id,
                "tier": user_info.tier,
                "isActive": user_info.isActive,
                "description": user_info.description,
                "createdAt": user_info.createdAt,
                "updatedAt": user_info.updatedAt,
                "number_of_links": len(user_info.links),
                "links": [
                    {"url": link.url, "button_text": link.button_text}
                    for link in user_info.links
                ],
            }

            logger.info(f"Complete process successful for user: {username}")

            return {
                "success": True,
                "message": "Processo completo realizado com sucesso. Dados processados e enviados para Firebase.",
                "data": user_data,
                "firebase_url": firebase_url,
                "username": username,
                "linkpage": f"https://link.avenca.site/{normalize_username(username)}",
            }

        except FileNotFoundError as e:
            error_message = str(e)
            logger.error(f"File not found error for user {username}: {error_message}")
            return {
                "success": False,
                "message": f"Arquivo de dados do usuário não encontrado: {error_message}",
                "data": None,
            }

        except json.JSONDecodeError as e:
            error_message = str(e)
            logger.error(f"JSON decode error for user {username}: {error_message}")
            return {
                "success": False,
                "message": f"Erro ao decodificar dados JSON do usuário: {error_message}",
                "data": None,
            }

        except Exception as e:
            error_message = str(e)
            logger.error(
                f"Error in complete process for user {username}: {error_message}"
            )

            # Verificar se é um erro 404 (perfil não encontrado)
            if "404" in error_message or "Not Found" in error_message:
                return {
                    "success": False,
                    "message": f"Perfil '{username}' não encontrado no Linktree. Verifique se o nome de usuário está correto e se o perfil existe.",
                    "data": None,
                }

            # Outros erros de rede
            elif "ClientResponseError" in error_message:
                return {
                    "success": False,
                    "message": f"Erro de conexão ao acessar o perfil '{username}'. O perfil pode estar indisponível ou não existir.",
                    "data": None,
                }

            # Erro no upload para Firebase
            elif "Firebase" in error_message or "upload" in error_message.lower():
                return {
                    "success": False,
                    "message": f"Erro ao enviar dados para Firebase: {error_message}",
                    "data": None,
                }

            # Outros erros gerais
            return {
                "success": False,
                "message": f"Erro durante o processo completo: {error_message}",
                "data": None,
            }


# Instância global do serviço
scraper_service = ScraperService()
