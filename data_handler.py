from typing import Dict, Any
import requests
import base64
from dotenv import load_dotenv

from core.constants import FIREBASE_URL, GEMINI_API_KEY, OPENAI_API_KEY
from services.utils.normalize_username import normalize_username
from services.imgbb_service import ImgBBService
from services.genai_service import AiDataTransformer

# Load environment variables
load_dotenv()


def transform_with_ai(
    input_json: Dict[str, Any], model: str = "gemini", additional_info: str = ""
) -> Dict[str, Any]:
    """
    Transform JSON data using AI to optimize it for link-in-bio pages.

    Args:
        input_json: The JSON data to transform
        model: AI model to use ("gemini" or "openai")
        additional_info: Additional context information for processing
    """
    transformer = AiDataTransformer(
        gemini_api_key=GEMINI_API_KEY, openai_api_key=OPENAI_API_KEY
    )
    return transformer.transform(
        input_json, model=model, additional_info=additional_info
    )


async def process_and_upload_json(
    input_json: Dict[str, Any], use_ai: bool = True, additional_info: str = ""
) -> Dict[str, Any]:
    """
    Process JSON data, upload avatars, and upload the final JSON to Firebase.

    Args:
        input_json: The JSON data to process
        use_ai: Whether to use AI transformation
        additional_info: Additional context information for AI processing
    """
    try:
        # Transform with AI if requested
        if use_ai:
            input_json = transform_with_ai(input_json, additional_info=additional_info)
            print("[INFO] AI transformation completed")

        # Process each user profile
        for username, profile_data in input_json.items():
            print(f"[INFO] Processing user: {username}")

            # Get the photo URL from the user data structure
            if isinstance(profile_data, dict) and "user" in profile_data:
                user_data = profile_data["user"]
                if isinstance(user_data, dict) and "avatar" in user_data:
                    avatar_url = user_data["avatar"]
                    print(f"[INFO] Found avatar URL: {avatar_url}")

                    # Read local file and convert to base64
                    try:
                        with open(avatar_url, "rb") as image_file:
                            encoded_string = base64.b64encode(image_file.read()).decode(
                                "utf-8"
                            )

                        # Upload avatar to ImgBB
                        normalized_username = normalize_username(username)
                        imgbb_service = ImgBBService()
                        new_avatar_url = await imgbb_service.upload_image(
                            encoded_string, name=f"{normalized_username}_avatar"
                        )
                        print(f"[INFO] New avatar URL: {new_avatar_url}")

                        # Update the URL in the JSON
                        profile_data["user"]["avatar"] = new_avatar_url
                    except FileNotFoundError:
                        print(f"[ERROR] Avatar file not found: {avatar_url}")
                        profile_data["user"]["avatar"] = ""
                    except Exception as e:
                        print(f"[ERROR] Failed to process avatar: {str(e)}")
                        profile_data["user"]["avatar"] = ""

        # Upload to Firebase
        print("[INFO] Preparing Firebase upload...")

        # Normalize username for Firebase upload
        normalized_username = normalize_username(username)
        firebase_url = f"{FIREBASE_URL}/{normalized_username}.json"
        print(f"[INFO] Firebase URL: {firebase_url}")

        # Upload the specific user data
        user_data = input_json[username]
        response = requests.patch(firebase_url, json=user_data)
        response.raise_for_status()
        print("[INFO] Data successfully uploaded to Firebase")

        # Save local copy
        # normalized_username = normalize_username(username)
        # os.makedirs(f"output/{normalized_username}", exist_ok=True)
        # output_path = f"output/{normalized_username}/fb_{normalized_username}.json"
        # with open(output_path, "w", encoding="utf-8") as f:
        #     json.dump(user_data, f, ensure_ascii=False, indent=2)
        # print(f"[INFO] Saved local copy to {output_path}")

        return input_json

    except Exception as e:
        print(f"[ERROR] Error processing JSON: {str(e)}")
        print(f"[ERROR] Full error details: {repr(e)}")
        raise
